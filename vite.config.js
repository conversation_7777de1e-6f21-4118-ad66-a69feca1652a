import { searchForWorkspaceRoot, loadEnv } from 'vite';
import fs from 'fs';
import path from 'path';

export default ( { mode } ) => {

	process.env = { ...process.env, ...loadEnv( mode, process.cwd() ) };

	// Safely read HTML files from example directory with error handling
	let htmlFiles = [];
	try {
		const exampleDir = './example/';
		if (fs.existsSync(exampleDir)) {
			const files = fs.readdirSync(exampleDir);
			htmlFiles = files
				.filter(p => /\.html$/.test(p))
				.map(p => path.join(exampleDir, p));
		}
	} catch (error) {
		console.warn('Warning: Could not read example directory:', error.message);
		// Fallback to known HTML files
		htmlFiles = [
			'./example/addTilesetExample.html',
			'./example/firstPersonControl.html'
		].filter(file => {
			try {
				return fs.existsSync(file);
			} catch {
				return false;
			}
		});
	}

	// Ensure we have at least one entry point
	if (htmlFiles.length === 0) {
		htmlFiles = ['./example/addTilesetExample.html'];
	}

	return {

		root: './example/',
		envDir: '.',
		base: '',
		build: {
			outDir: './bundle/',
			rollupOptions: {
				input: htmlFiles,
			},
		},
		server: {
			fs: {
				allow: [
					// search up for workspace root
					searchForWorkspaceRoot( process.cwd() ),
				],
			},
		}
	}

};
